# Vault OIDC Configuration Environment Variables
# Copy this file to .env.vault and fill in the values

# Vault Configuration
VAULT_ADDR=https://vault-teams.idp-test.deva.esc.esetrs.cz
VAULT_TOKEN=your-root-token-here

# Azure AD OIDC Configuration
AAD_CLIENT_ID=your-azure-ad-client-id
AAD_CLIENT_SECRET=your-azure-ad-client-secret
AAD_TENANT_ID=your-azure-ad-tenant-id

# Vault OIDC Settings
OIDC_DEFAULT_ROLE=azure_users
OIDC_DISCOVERY_URL=https://login.microsoftonline.com/${AAD_TENANT_ID}/v2.0
OIDC_SCOPES="https://graph.microsoft.com/.default profile"

# Redirect URIs (space-separated)
VAULT_REDIRECT_URIS="https://vault-teams.idp-test.deva.esc.esetrs.cz/ui/vault/auth/oidc/oidc/callback http://localhost:8250/oidc/callback"

# Azure AD Groups (Group Object IDs from Azure AD)
AAD_ADMIN_GROUP_ID=your-admin-group-object-id
AAD_DEVELOPER_GROUP_ID=your-developer-group-object-id
AAD_READONLY_GROUP_ID=your-readonly-group-object-id

# Token Settings
ADMIN_TOKEN_TTL=4h
ADMIN_TOKEN_MAX_TTL=12h
DEVELOPER_TOKEN_TTL=8h
DEVELOPER_TOKEN_MAX_TTL=24h
READONLY_TOKEN_TTL=8h
READONLY_TOKEN_MAX_TTL=24h

# KV Secrets Engine Path
KV_PATH=secret

# Project-specific settings
AAD_SIGPROJECT_GROUP_ID=sigproject-group-object-id
SIGPROJECT_TOKEN_TTL=6h
SIGPROJECT_TOKEN_MAX_TTL=18h

# Default token settings for azure_users
DEFAULT_TOKEN_TTL=8h