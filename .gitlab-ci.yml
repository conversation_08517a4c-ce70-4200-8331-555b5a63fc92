# GitLab CI Pipeline for Vault OIDC Configuration Repository
# Three-phase approach: Generate, Plan, then Deploy

stages:
  - generate
  - plan
  - deploy

variables:
  VAULT_VERSION: "1.15.2"
  # Pipeline uses .env.vault defaults with environment variable overrides
  # Only secrets need to be set as GitLab CI/CD variables for security

# Generate configurations when vault configuration changes (excluding generated/)
generate-vault-configs:
  stage: generate
  image: alpine:latest
  before_script:
    - apk add --no-cache bash yq jq gettext
  script:
    - echo "Generating Vault configurations using .env.vault defaults..."
    - echo "Pipeline variables can override any .env.vault values"
    - ./parse-projects.sh generate-all
    - echo "Generated configurations:"
    - find generated -type f -name "*.json" -o -name "*.hcl" | sort
  artifacts:
    paths:
      - generated/
    expire_in: 1 day # Keep artifact for the plan job
  rules:
    # Run when configuration changes, but exclude changes to generated/ folder
    - if: '$CI_PIPELINE_SOURCE == "push"'
      changes:
        - "**/*"
        - "!generated/**/*"
    # Also run manually
    - when: manual
      allow_failure: false
  after_script:
    - echo "Generated $(find generated -type f | wc -l) files at $(date)"

# Create a plan of changes to be applied
plan-changes:
  stage: plan
  image: alpine:latest
  needs: ["generate-vault-configs"]
  before_script:
    - apk add --no-cache bash git gettext curl jq unzip
    # Install Vault CLI
    - wget -O vault.zip https://releases.hashicorp.com/vault/${VAULT_VERSION}/vault_${VAULT_VERSION}_linux_amd64.zip
    - unzip vault.zip
    - mv vault /usr/local/bin/
    - chmod +x /usr/local/bin/vault
    # Load environment variables from .env.vault
    - set -a
    - test -f .env.vault && source .env.vault
    - set +a
  script:
    - |
      cat > plan.txt <<EOF
      Plan for Vault deployment

      ##################################################
      ### Changes to generated configuration files ###
      ##################################################
      EOF
    - git diff --stat -- generated/ >> plan.txt
    - |
      cat >> plan.txt <<EOF

      ##################################################
      ### Dry-run of Vault configuration changes ###
      ##################################################
      EOF
    # Run dry-run and strip ANSI color codes
    - ./setup-vault-oidc.sh --dry-run | sed 's/\x1b\[[0-9;]*m//g' >> plan.txt
    - echo "Plan generated. Review the plan.txt artifact or the log output below."
    - echo "-------------------- PLAN PREVIEW --------------------"
    - cat plan.txt
    - echo "------------------ END PLAN PREVIEW ------------------"
  artifacts:
    paths:
      - plan.txt
    expire_in: 1 day
  rules:
    # Run when configuration changes, but exclude changes to generated/ folder
    - if: '$CI_PIPELINE_SOURCE == "push"'
      changes:
        - "**/*"
        - "!generated/**/*"
    # Also run manually
    - when: manual
      allow_failure: false

# Deploy to Vault manually after reviewing the plan
deploy-to-vault:
  stage: deploy
  image: alpine:latest
  needs: ["plan-changes"]
  before_script:
    - apk add --no-cache bash curl jq gettext unzip
    # Load environment variables from .env.vault
    - set -a
    - test -f .env.vault && source .env.vault
    - set +a
    # Install Vault CLI
    - wget -O vault.zip https://releases.hashicorp.com/vault/${VAULT_VERSION}/vault_${VAULT_VERSION}_linux_amd64.zip
    - unzip vault.zip
    - mv vault /usr/local/bin/
    - chmod +x /usr/local/bin/vault
  script:
    - echo "Deploying Vault configurations..."
    # Validate required secret environment variables (must be set in pipeline)
    - |
      if [ -z "$VAULT_TOKEN" ]; then
        echo "ERROR: VAULT_TOKEN must be set as GitLab CI/CD variable"
        exit 1
      fi
      if [ -z "$AAD_CLIENT_SECRET" ]; then
        echo "ERROR: AAD_CLIENT_SECRET must be set as GitLab CI/CD variable"
        exit 1
      fi
    # Test Vault connectivity
    - vault status
    # Deploy configurations (script handles env override automatically)
    - ./setup-vault-oidc.sh
  rules:
    # Run manually on protected branches
    - if: '$CI_COMMIT_BRANCH == "main" || $CI_COMMIT_BRANCH == "master" || $CI_COMMIT_BRANCH == "develop"'
      when: manual
      allow_failure: false
  environment:
    name: vault-production
    url: $VAULT_ADDR

# Validation job - runs on all changes to validate configuration
validate-config:
  stage: generate
  image: alpine:latest
  before_script:
    - apk add --no-cache bash yq jq
  script:
    - echo "Validating Vault configuration..."
    - ./parse-projects.sh validate
    - echo "✅ Configuration is valid"
  rules:
    - changes:
        - "**/*"
    - when: manual
      allow_failure: true

