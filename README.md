# Simplified Vault OIDC Configuration

A clean, two-phase system for managing HashiCorp Vault OIDC authentication with Azure AD.

## 🎯 Overview

**Two-Phase Approach:**
1. **Generate**: Create deployment-ready configurations from templates
2. **Deploy**: Apply configurations to Vault

**Key Benefits:**
- Simple and easy to understand
- Clean separation of generation and deployment
- Environment variable override support
- GitLab CI/CD integration

## 📁 Structure

```
vault/
├── setup-vault-oidc.sh          # Deployment script
├── parse-projects.sh            # Generation script
├── Taskfile.yml                 # Task automation
├── .env.vault                   # Environment variables
├── config/                      # Static configuration
│   ├── projects.yml             # Project definitions
│   ├── oidc-config.json.template
│   └── policy-*.hcl, role-*.json.template
└── generated/                   # Generated files (deployment-ready)
    ├── oidc-config.json         # OIDC config with actual values
    ├── policies/                # All policies with actual values
    └── roles/                   # All roles with actual values
```

## 🚀 Quick Start

1. **Configure Environment**
   ```bash
   # Edit .env.vault with your values
   vim .env.vault
   ```

2. **Configure Projects**
   ```bash
   # Edit config/projects.yml to define your projects
   vim config/projects.yml
   ```

3. **Deploy to Vault**
   ```bash
   task setup    # Generate + Deploy
   # OR
   task dry-run  # Preview what would be deployed
   ```

## 📋 Available Tasks

```bash
# Core Tasks
task setup        # Generate configurations and deploy to Vault
task deploy       # Deploy generated configurations to Vault
task dry-run      # Show what would be deployed

# Project Management
task list-projects     # List all configured projects and base roles
task validate-projects # Validate projects.yml configuration
task generate-all      # Generate all configurations

# Utility
task show-generated    # Show generated folder structure
task clean            # Clean up temporary files and generated folder
task help             # Show available tasks
```

## 🔧 Configuration

### Environment Variables (.env.vault)
- Uses `.env.vault` file for default values
- Environment variables override file values
- Secrets (TOKEN, SECRET) handled securely during deployment

### Project Configuration (config/projects.yml)
```yaml
projects:
  sigproject:
    group_id: "${AAD_SIGPROJECT_GROUP_ID}"  # Mandatory
    token_ttl: "6h"                         # Optional
    # Custom policy template (optional)
```

### Adding New Projects
1. Add project to `config/projects.yml`
2. Add required environment variables to `.env.vault`
3. Run `task setup`

## 🔄 GitLab CI Integration

The system includes GitLab CI jobs:
- **`generate-vault-configs`**: Runs when `vault/` folder changes
- **`deploy-to-vault`**: Runs when `vault/generated/` folder changes
- Uses `.env.vault` defaults with pipeline variable overrides

### 1. Prerequisites

**Azure AD Setup** (follow the detailed guide in `../wiki/vault/AAD vault.md`):
- Create Azure AD App Registration
- Configure OIDC settings and redirect URIs
- Create security groups (Admin, Developer, ReadOnly)
- Assign groups to the application

**System Requirements**:
```bash
# Install required tools
vault --version  # Vault CLI
envsubst --version  # For template substitution (part of gettext)
```

### 2. Configuration

```bash
# 1. Copy and customize environment file
cp .env.vault .env.vault.local
vi .env.vault.local  # Fill in your Azure AD values

# 2. Set environment variables
export VAULT_ADDR='https://vault-teams.idp-test.deva.esc.esetrs.cz'
export VAULT_TOKEN='your-root-token'
```

### 3. Run Setup

```bash
# Full setup
./setup-vault-oidc.sh --env-file .env.vault.local

# Dry run (see what would be done)
./setup-vault-oidc.sh --env-file .env.vault.local --dry-run

# Verify existing configuration
./setup-vault-oidc.sh --env-file .env.vault.local --verify-only
```

## 🔧 Configuration Details

### Environment Variables

Key variables in `.env.vault`:

```bash
# Azure AD Configuration
AAD_CLIENT_ID=your-azure-ad-client-id
AAD_CLIENT_SECRET=your-azure-ad-client-secret  
AAD_TENANT_ID=your-azure-ad-tenant-id

# Azure AD Groups (Object IDs)
AAD_ADMIN_GROUP_ID=admin-group-object-id
AAD_DEVELOPER_GROUP_ID=developer-group-object-id
AAD_READONLY_GROUP_ID=readonly-group-object-id
```

### Access Control Matrix

| Role | Azure AD Group | Vault Policies | Access Level |
|------|---------------|----------------|--------------|
| Admin | `AAD_ADMIN_GROUP_ID` | `admin` | Full access to all paths |
| Developer | `AAD_DEVELOPER_GROUP_ID` | `developer` | Full access to `dev/*` and `projects/*`, read-only elsewhere |
| ReadOnly | `AAD_READONLY_GROUP_ID` | `readonly` | Read-only access to all secrets |
| Default | All groups | `default` | Basic access to `shared/*` secrets |

### Secrets Organization

The setup assumes the following KV structure:
```
secret/
├── shared/          # Accessible to all users
├── dev/             # Full access for developers
├── projects/        # Full access for developers
└── admin/           # Admin only (via admin policy)
```

## 🧪 Testing

### 1. CLI Authentication
```bash
# Test admin login
vault login -method=oidc role=admin

# Test developer login  
vault login -method=oidc role=developer

# Test readonly login
vault login -method=oidc role=readonly

# Check your token and policies
vault token lookup
```

### 2. Web UI Authentication
1. Navigate to `https://vault-teams.idp-test.deva.esc.esetrs.cz/ui/`
2. Select "OIDC" authentication method
3. You'll be redirected to Azure AD for authentication
4. After successful login, verify your access level

### 3. Access Testing
```bash
# After logging in as developer
vault kv put secret/dev/myapp api_key=secret123      # ✅ Should work
vault kv put secret/projects/web db_url=postgres://  # ✅ Should work  
vault kv put secret/admin/root master=key            # ❌ Should fail
vault kv get secret/shared/common                    # ✅ Should work (read-only)
```

## 🔄 Alternative Approaches

Instead of shell scripts, consider these alternatives:

### 1. **Terraform** (Recommended for Production)
```hcl
# terraform/vault-oidc.tf
resource "vault_auth_backend" "oidc" {
  type = "oidc"
}

resource "vault_oidc_auth_backend" "azure" {
  discovery_url = "https://login.microsoftonline.com/${var.tenant_id}/v2.0"
  client_id     = var.client_id
  client_secret = var.client_secret
  
  provider_config = {
    provider = "azure"
  }
}
```

### 2. **Ansible**
```yaml
# playbook.yml
- name: Configure Vault OIDC
  hashivault_auth_method:
    method_type: oidc
    config:
      oidc_discovery_url: "{{ oidc_discovery_url }}"
      oidc_client_id: "{{ client_id }}"
      oidc_client_secret: "{{ client_secret }}"
```

### 3. **Vault Configuration as Code (VCC)**
```yaml
# vault-config.yaml
auth:
  - type: oidc
    config:
      oidc_discovery_url: ${OIDC_DISCOVERY_URL}
      oidc_client_id: ${AAD_CLIENT_ID}
      oidc_client_secret: ${AAD_CLIENT_SECRET}
```

### 4. **GitOps with ArgoCD**
Use Vault's configuration drift detection with Git-stored configs.

## 🛠 Maintenance

### Rotating Client Secrets
```bash
# Update Azure AD client secret in environment file
vi .env.vault.local

# Re-run OIDC configuration only
vault write auth/oidc/config -<<EOF
{
  "oidc_client_secret": "new-secret-value"
}
EOF
```

### Adding New Groups
1. Create new Azure AD security group
2. Get the Object ID
3. Add to `.env.vault.local`
4. Create new role template
5. Run setup script

### Monitoring
```bash
# Check auth methods
vault auth list

# View OIDC configuration  
vault read auth/oidc/config

# List and inspect roles
vault list auth/oidc/role
vault read auth/oidc/role/admin

# Monitor authentication
vault audit list
vault audit enable file file_path=/var/log/vault-audit.log
```

## 🚨 Troubleshooting

### Common Issues

1. **"Field validation error for provider_config"**
   - Ensure entire OIDC config is valid JSON
   - Use the provided templates

2. **"Redirect URI mismatch"**
   - Verify URIs in Azure AD exactly match Vault configuration
   - Check for trailing slashes and case sensitivity

3. **"No groups in token"**
   - Verify Azure AD groups claim configuration
   - Check group assignments in Enterprise Application

4. **"Permission denied"**
   - Verify user is member of correct Azure AD group
   - Check group Object ID matches configuration
   - Ensure group is assigned to Azure AD application

### Debug Commands
```bash
# Detailed authentication debugging
vault login -method=oidc -log-level=debug

# Check role configuration
vault read auth/oidc/role/admin

# Test token information
vault token lookup

# Verify policies
vault policy read admin
```

## 📚 References

- [Azure AD OIDC Guide](../wiki/vault/AAD%20vault.md) - Detailed Azure AD setup
- [Vault Setup Guide](../wiki/vault/setup%20in%20vault.md) - Vault configuration details
- [HashiCorp Vault OIDC Documentation](https://developer.hashicorp.com/vault/docs/auth/jwt/oidc-providers/azuread)
- [Azure AD OIDC Documentation](https://docs.microsoft.com/en-us/azure/active-directory/develop/v2-protocols-oidc)

## 🤝 Contributing

When making changes:
1. Test in development environment first
2. Update templates and documentation
3. Verify backward compatibility
4. Test all roles and access levels