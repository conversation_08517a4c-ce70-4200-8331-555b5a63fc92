#!/bin/bash

# Simplified Vault OIDC Deployment Script
# Deploys generated configurations to Vault

set -euo pipefail

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Script directory
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
GENERATED_DIR="${SCRIPT_DIR}/generated"
ENV_FILE="${SCRIPT_DIR}/.env.vault"

# Function to print colored output
print_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to check if required commands are available
check_dependencies() {
    if ! command -v vault &> /dev/null; then
        print_error "Vault CLI is not installed or not in PATH"
        exit 1
    fi

    if ! command -v envsubst &> /dev/null; then
        print_error "envsubst is not installed (usually part of gettext package)"
        exit 1
    fi
}

# Function to load environment variables with override support
load_env() {
    if [[ ! -f "$ENV_FILE" ]]; then
        print_error "Environment file not found: $ENV_FILE"
        exit 1
    fi

    # Source the env file (environment variables override file values)
    set -a
    source "$ENV_FILE"
    set +a

    # Validate required variables
    local required_vars=("VAULT_ADDR" "VAULT_TOKEN" "AAD_CLIENT_ID" "AAD_CLIENT_SECRET")
    for var in "${required_vars[@]}"; do
        if [[ -z "${!var}" ]]; then
            print_error "Required variable $var is not set"
            exit 1
        fi
    done
}

# Function to test Vault connectivity
test_vault_connection() {
    if ! vault status &> /dev/null; then
        print_error "Cannot connect to Vault at $VAULT_ADDR"
        exit 1
    fi
}

# Function to enable OIDC auth method
enable_oidc_auth() {
    local dry_run=${1:-false}

    if [[ "$dry_run" == true ]]; then
        print_info "[DRY RUN] Would enable OIDC authentication method"
        return
    fi

    print_info "Enabling OIDC authentication method..."
    if vault auth list | grep -q "oidc/"; then
        print_warning "OIDC auth method already enabled"
    else
        vault auth enable oidc
        print_success "OIDC auth method enabled"
    fi
}



# Function to create and apply Vault policies
create_policies() {
    local dry_run=${1:-false}

    # Check if generated directory exists
    if [[ ! -d "$GENERATED_DIR/policies" ]]; then
        print_error "Generated policies directory not found: $GENERATED_DIR/policies"
        print_error "Please run './parse-projects.sh generate-all' first"
        exit 1
    fi

    if [[ "$dry_run" == true ]]; then
        print_info "[DRY RUN] Would create policies:"
        for policy_file in "$GENERATED_DIR"/policies/policy-*.hcl; do
            if [[ -f "$policy_file" ]]; then
                local policy_name
                policy_name=$(basename "$policy_file" .hcl | sed 's/^policy-//')
                print_info "  - $policy_name"
            fi
        done
        return
    fi

    print_info "Creating Vault policies from generated configurations..."
    for policy_file in "$GENERATED_DIR"/policies/policy-*.hcl; do
        if [[ -f "$policy_file" ]]; then
            local policy_name
            policy_name=$(basename "$policy_file" .hcl | sed 's/^policy-//')
            vault policy write "$policy_name" "$policy_file"
            print_success "Policy '$policy_name' created/updated"
        fi
    done
}

# Function to configure OIDC
configure_oidc() {
    local dry_run=${1:-false}
    local oidc_config="${GENERATED_DIR}/oidc-config.json"

    # Check if generated OIDC config exists
    if [[ ! -f "$oidc_config" ]]; then
        print_error "Generated OIDC config not found: $oidc_config"
        print_error "Please run './parse-projects.sh generate-all' first"
        exit 1
    fi

    if [[ "$dry_run" == true ]]; then
        print_info "[DRY RUN] Would configure OIDC authentication with Azure AD"
        return
    fi

    print_info "Configuring OIDC authentication..."
    local temp_config
    temp_config=$(mktemp)

    # Substitute secrets in the generated config (non-secrets already substituted)
    envsubst < "$oidc_config" > "$temp_config"

    # Apply OIDC configuration
    vault write auth/oidc/config @"$temp_config"
    print_success "OIDC configuration applied"

    # Clean up temp file
    rm -f "$temp_config"
}

# Function to get list of managed policies (policies that were created by this system)
get_managed_policies() {
    # Get all policies from Vault, excluding built-in ones
    # Only consider policies that match our naming pattern (admin, default, readonly, project names)
    vault policy list | grep -v "^default$" | grep -v "^root$" | grep -E "^(admin|readonly|[a-zA-Z0-9_-]+)$" || true
}

# Function to get list of managed OIDC roles (roles that were created by this system)
get_managed_oidc_roles() {
    # Get all OIDC roles from Vault
    vault list auth/oidc/role 2>/dev/null | tail -n +3 || true
}

# Function to clean up unused policies
cleanup_unused_policies() {
    local dry_run=${1:-false}

    print_info "Checking for unused policies to clean up..."

    # Get current policies from Vault
    local vault_policies
    vault_policies=$(get_managed_policies)

    # Get expected policies from generated files
    local expected_policies=()
    if [[ -d "$GENERATED_DIR/policies" ]]; then
        for policy_file in "$GENERATED_DIR"/policies/policy-*.hcl; do
            if [[ -f "$policy_file" ]]; then
                local policy_name
                policy_name=$(basename "$policy_file" .hcl | sed 's/^policy-//')
                expected_policies+=("$policy_name")
            fi
        done
    fi

    # Find policies to remove (with safety checks)
    local policies_to_remove=()
    local protected_policies=("default" "root")  # Never remove these

    while IFS= read -r policy; do
        if [[ -n "$policy" ]]; then
            # Check if it's a protected policy
            local is_protected=false
            for protected in "${protected_policies[@]}"; do
                if [[ "$policy" == "$protected" ]]; then
                    is_protected=true
                    break
                fi
            done

            if [[ "$is_protected" == false ]]; then
                local found=false
                for expected in "${expected_policies[@]}"; do
                    if [[ "$policy" == "$expected" ]]; then
                        found=true
                        break
                    fi
                done
                if [[ "$found" == false ]]; then
                    policies_to_remove+=("$policy")
                fi
            fi
        fi
    done <<< "$vault_policies"

    # Remove unused policies
    if [[ ${#policies_to_remove[@]} -gt 0 ]]; then
        if [[ "$dry_run" == true ]]; then
            print_warning "[DRY RUN] Would remove unused policies:"
            for policy in "${policies_to_remove[@]}"; do
                print_warning "  - $policy"
            done
        else
            print_warning "Removing unused policies:"
            for policy in "${policies_to_remove[@]}"; do
                vault policy delete "$policy"
                print_success "Policy '$policy' removed"
            done
        fi
    else
        print_info "No unused policies found"
    fi
}

# Function to clean up unused OIDC roles
cleanup_unused_oidc_roles() {
    local dry_run=${1:-false}

    print_info "Checking for unused OIDC roles to clean up..."

    # Get current roles from Vault
    local vault_roles
    vault_roles=$(get_managed_oidc_roles)

    # Get expected roles from generated files
    local expected_roles=()
    if [[ -d "$GENERATED_DIR/roles" ]]; then
        for role_file in "$GENERATED_DIR"/roles/role-*.json; do
            if [[ -f "$role_file" ]]; then
                local role_name
                role_name=$(basename "$role_file" .json | sed 's/^role-//')
                local vault_role_name="${role_name//-/_}"
                expected_roles+=("$vault_role_name")
            fi
        done
    fi

    # Find roles to remove
    local roles_to_remove=()
    while IFS= read -r role; do
        if [[ -n "$role" ]]; then
            local found=false
            for expected in "${expected_roles[@]}"; do
                if [[ "$role" == "$expected" ]]; then
                    found=true
                    break
                fi
            done
            if [[ "$found" == false ]]; then
                roles_to_remove+=("$role")
            fi
        fi
    done <<< "$vault_roles"

    # Remove unused roles
    if [[ ${#roles_to_remove[@]} -gt 0 ]]; then
        if [[ "$dry_run" == true ]]; then
            print_warning "[DRY RUN] Would remove unused OIDC roles:"
            for role in "${roles_to_remove[@]}"; do
                print_warning "  - $role"
            done
        else
            print_warning "Removing unused OIDC roles:"
            for role in "${roles_to_remove[@]}"; do
                vault delete "auth/oidc/role/$role"
                print_success "OIDC role '$role' removed"
            done
        fi
    else
        print_info "No unused OIDC roles found"
    fi
}

# Function to create OIDC roles
create_oidc_roles() {
    local dry_run=${1:-false}

    # Check if generated directory exists
    if [[ ! -d "$GENERATED_DIR/roles" ]]; then
        print_error "Generated roles directory not found: $GENERATED_DIR/roles"
        print_error "Please run './parse-projects.sh generate-all' first"
        exit 1
    fi

    if [[ "$dry_run" == true ]]; then
        print_info "[DRY RUN] Would create OIDC roles:"
        for role_file in "$GENERATED_DIR"/roles/role-*.json; do
            if [[ -f "$role_file" ]]; then
                local role_name
                role_name=$(basename "$role_file" .json | sed 's/^role-//')
                local vault_role_name="${role_name//-/_}"
                print_info "  - $vault_role_name"
            fi
        done
        return
    fi

    print_info "Creating OIDC roles from generated configurations..."
    for role_file in "$GENERATED_DIR"/roles/role-*.json; do
        if [[ -f "$role_file" ]]; then
            local role_name
            role_name=$(basename "$role_file" .json | sed 's/^role-//')
            local vault_role_name="${role_name//-/_}"
            vault write "auth/oidc/role/${vault_role_name}" @"$role_file"
            print_success "OIDC role '$vault_role_name' created/updated"
        fi
    done
}

# Function to get OIDC mount accessor
get_oidc_mount_accessor() {
    vault auth list -format=json | jq -r '.["oidc/"].accessor'
}

# Function to create identity groups
create_identity_groups() {
    local dry_run=${1:-false}

    # Check if identity groups directory exists
    if [[ ! -d "$GENERATED_DIR/identity-groups" ]]; then
        print_info "No identity groups directory found, skipping identity groups creation"
        return
    fi

    if [[ "$dry_run" == true ]]; then
        print_info "[DRY RUN] Would create identity groups:"
        for group_file in "$GENERATED_DIR"/identity-groups/group-*.json; do
            if [[ -f "$group_file" ]]; then
                local group_name
                group_name=$(basename "$group_file" .json | sed 's/^group-//')
                print_info "  - $group_name"
            fi
        done
        return
    fi

    print_info "Creating identity groups from generated configurations..."
    
    # Create identity groups first
    for group_file in "$GENERATED_DIR"/identity-groups/group-*.json; do
        if [[ -f "$group_file" ]]; then
            local group_name
            group_name=$(basename "$group_file" .json | sed 's/^group-//')
            
            # Check if group already exists
            if vault read "identity/group/name/$group_name" &>/dev/null; then
                print_warning "Identity group '$group_name' already exists, updating..."
                vault write "identity/group/name/$group_name" @"$group_file"
            else
                vault write identity/group @"$group_file"
            fi
            print_success "Identity group '$group_name' created/updated"
        fi
    done
}

# Function to create group aliases
create_group_aliases() {
    local dry_run=${1:-false}

    # Check if identity groups directory exists
    if [[ ! -d "$GENERATED_DIR/identity-groups" ]]; then
        print_info "No identity groups directory found, skipping group aliases creation"
        return
    fi

    if [[ "$dry_run" == true ]]; then
        print_info "[DRY RUN] Would create group aliases:"
        for alias_file in "$GENERATED_DIR"/identity-groups/alias-*.json; do
            if [[ -f "$alias_file" ]]; then
                local alias_name
                alias_name=$(basename "$alias_file" .json | sed 's/^alias-//')
                print_info "  - $alias_name"
            fi
        done
        return
    fi

    # Get OIDC mount accessor
    local mount_accessor
    mount_accessor=$(get_oidc_mount_accessor)
    
    if [[ -z "$mount_accessor" || "$mount_accessor" == "null" ]]; then
        print_error "Could not get OIDC mount accessor. Make sure OIDC auth is enabled."
        exit 1
    fi

    print_info "Creating group aliases from generated configurations..."
    
    for alias_file in "$GENERATED_DIR"/identity-groups/alias-*.json; do
        if [[ -f "$alias_file" ]]; then
            local alias_name
            alias_name=$(basename "$alias_file" .json | sed 's/^alias-//')
            
            # Get the corresponding group's canonical ID
            local canonical_id
            canonical_id=$(vault read "identity/group/name/$alias_name" -format=json | jq -r '.data.id')
            
            if [[ -z "$canonical_id" || "$canonical_id" == "null" ]]; then
                print_error "Could not get canonical ID for group: $alias_name"
                continue
            fi
            
            # Create a secure temporary file with all substitutions
            local temp_alias_file
            temp_alias_file=$(mktemp)

            # Set environment variables for substitution
            export MOUNT_ACCESSOR="$mount_accessor"
            export CANONICAL_ID="$canonical_id"

            # Apply substitution
            envsubst < "$alias_file" > "$temp_alias_file"
            
            # Clean up environment variables
            unset MOUNT_ACCESSOR CANONICAL_ID
            
            # Check if group alias already exists for this group and mount
            local existing_alias_id=""
            existing_alias_id=$(vault list identity/group-alias/id 2>/dev/null | tail -n +3 | while read -r alias_id; do
                if [[ -n "$alias_id" ]]; then
                    local alias_data
                    alias_data=$(vault read "identity/group-alias/id/$alias_id" -format=json 2>/dev/null)
                    local alias_canonical_id alias_mount
                    alias_canonical_id=$(echo "$alias_data" | jq -r '.data.canonical_id')
                    alias_mount=$(echo "$alias_data" | jq -r '.data.mount_accessor')
                    if [[ "$alias_canonical_id" == "$canonical_id" && "$alias_mount" == "$mount_accessor" ]]; then
                        echo "$alias_id"
                        break
                    fi
                fi
            done)
            
            # Create or update the group alias
            if [[ -n "$existing_alias_id" ]]; then
                print_warning "Group alias for '$alias_name' already exists, updating..."
                vault write "identity/group-alias/id/$existing_alias_id" @"$temp_alias_file"
            else
                vault write identity/group-alias @"$temp_alias_file"
            fi
            print_success "Group alias for '$alias_name' created/updated"
            
            # Clean up temp file
            rm -f "$temp_alias_file"
        fi
    done
}

# Function to get managed identity groups
get_managed_identity_groups() {
    # Get all identity groups that have the managed_by metadata
    vault list identity/group/name 2>/dev/null | tail -n +3 | while read -r group_name; do
        if [[ -n "$group_name" ]]; then
            local managed_by
            managed_by=$(vault read "identity/group/name/$group_name" -format=json 2>/dev/null | jq -r '.data.metadata.managed_by // empty')
            if [[ "$managed_by" == "vault-oidc-automation" ]]; then
                echo "$group_name"
            fi
        fi
    done
}

# Function to get managed group aliases
get_managed_group_aliases() {
    # Get all group aliases for our OIDC mount
    local mount_accessor
    mount_accessor=$(get_oidc_mount_accessor)
    
    if [[ -n "$mount_accessor" && "$mount_accessor" != "null" ]]; then
        vault list identity/group-alias/id 2>/dev/null | tail -n +3 | while read -r alias_id; do
            if [[ -n "$alias_id" ]]; then
                local alias_mount
                alias_mount=$(vault read "identity/group-alias/id/$alias_id" -format=json 2>/dev/null | jq -r '.data.mount_accessor')
                if [[ "$alias_mount" == "$mount_accessor" ]]; then
                    echo "$alias_id"
                fi
            fi
        done
    fi
}

# Function to clean up unused identity groups
cleanup_unused_identity_groups() {
    local dry_run=${1:-false}

    print_info "Checking for unused identity groups to clean up..."

    # Get current managed groups from Vault
    local vault_groups
    vault_groups=$(get_managed_identity_groups)

    # Get expected groups from generated files
    local expected_groups=()
    if [[ -d "$GENERATED_DIR/identity-groups" ]]; then
        for group_file in "$GENERATED_DIR"/identity-groups/group-*.json; do
            if [[ -f "$group_file" ]]; then
                local group_name
                group_name=$(basename "$group_file" .json | sed 's/^group-//')
                expected_groups+=("$group_name")
            fi
        done
    fi

    # Find groups to remove
    local groups_to_remove=()
    while IFS= read -r group; do
        if [[ -n "$group" ]]; then
            local found=false
            for expected in "${expected_groups[@]}"; do
                if [[ "$group" == "$expected" ]]; then
                    found=true
                    break
                fi
            done
            if [[ "$found" == false ]]; then
                groups_to_remove+=("$group")
            fi
        fi
    done <<< "$vault_groups"

    # Remove unused groups
    if [[ ${#groups_to_remove[@]} -gt 0 ]]; then
        if [[ "$dry_run" == true ]]; then
            print_warning "[DRY RUN] Would remove unused identity groups:"
            for group in "${groups_to_remove[@]}"; do
                print_warning "  - $group"
            done
        else
            print_warning "Removing unused identity groups:"
            for group in "${groups_to_remove[@]}"; do
                # First get the group ID to remove aliases
                local group_id
                group_id=$(vault read "identity/group/name/$group" -format=json 2>/dev/null | jq -r '.data.id')
                
                # Remove group aliases first
                if [[ -n "$group_id" && "$group_id" != "null" ]]; then
                    vault list identity/group-alias/id 2>/dev/null | tail -n +3 | while read -r alias_id; do
                        if [[ -n "$alias_id" ]]; then
                            local canonical_id
                            canonical_id=$(vault read "identity/group-alias/id/$alias_id" -format=json 2>/dev/null | jq -r '.data.canonical_id')
                            if [[ "$canonical_id" == "$group_id" ]]; then
                                vault delete "identity/group-alias/id/$alias_id"
                            fi
                        fi
                    done
                fi
                
                # Remove the group
                vault delete "identity/group/name/$group"
                print_success "Identity group '$group' removed"
            done
        fi
    else
        print_info "No unused identity groups found"
    fi
}



# Function to show usage
show_usage() {
    echo "Usage: $0 [OPTIONS]"
    echo
    echo "Options:"
    echo "  --dry-run    Show what would be deployed without making changes"
    echo "  --cleanup    Clean up unused policies and roles (default: enabled)"
    echo "  --no-cleanup Skip cleanup of unused policies and roles"
    echo "  --help       Show this help message"
    echo
    echo "Examples:"
    echo "  $0                Deploy configurations to Vault (with cleanup)"
    echo "  $0 --dry-run      Preview what would be deployed"
    echo "  $0 --no-cleanup   Deploy without cleaning up unused items"
}

# Main execution
main() {
    local dry_run=false
    local cleanup=true

    # Parse command line arguments
    while [[ $# -gt 0 ]]; do
        case $1 in
            --dry-run)
                dry_run=true
                shift
                ;;
            --cleanup)
                cleanup=true
                shift
                ;;
            --no-cleanup)
                cleanup=false
                shift
                ;;
            --help)
                show_usage
                exit 0
                ;;
            *)
                print_error "Unknown option: $1"
                show_usage
                exit 1
                ;;
        esac
    done

    # Pre-flight checks
    check_dependencies
    load_env
    test_vault_connection

    if [[ "$dry_run" == true ]]; then
        print_info "DRY RUN MODE - No changes will be made"
    fi

    # Deploy configurations
    enable_oidc_auth "$dry_run"
    configure_oidc "$dry_run"
    create_policies "$dry_run"
    create_oidc_roles "$dry_run"
    
    # Create identity groups and aliases for automatic policy assignment
    create_identity_groups "$dry_run"
    create_group_aliases "$dry_run"

    # Clean up unused policies, roles, and identity groups if requested
    if [[ "$cleanup" == true ]]; then
        cleanup_unused_policies "$dry_run"
        cleanup_unused_oidc_roles "$dry_run"
        cleanup_unused_identity_groups "$dry_run"
    fi

    if [[ "$dry_run" == false ]]; then
        print_success "Vault OIDC deployment completed successfully!"
        if [[ "$cleanup" == true ]]; then
            print_info "Cleanup of unused policies and roles completed"
        fi
    else
        print_info "DRY RUN completed - no changes were made"
    fi
}

# Run main function
main "$@"